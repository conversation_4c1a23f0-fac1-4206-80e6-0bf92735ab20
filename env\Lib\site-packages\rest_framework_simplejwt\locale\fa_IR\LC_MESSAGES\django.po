# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-27 15:19+0330\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language: fa_IR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "هدر اعتبارسنجی باید شامل دو مقدار جدا شده با فاصله باشد"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "توکن داده شده برای هیچ نوع توکنی معتبر نمی‌باشد"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "توکن شامل هیچ شناسه قابل تشخیصی از کاربر نیست"

#: authentication.py:135
msgid "User not found"
msgstr "کاربر یافت نشد"

#: authentication.py:139
msgid "User is inactive"
msgstr "کاربر غیرفعال است"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr "رمز عبور کاربر تغییر کرده است"

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "نوع الگوریتم ناشناخته '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "برای استفاده از {} باید رمزنگاری را نصب کرده باشید."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr "نوع ناشناخته '{}'، 'leeway' باید از نوع int، float یا timedelta باشد."

#: backends.py:125 backends.py:177 tokens.py:69
msgid "Token is invalid"
msgstr "توکن نامعتبر است"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "الگوریتم نامعتبر مشخص شده است"

#: backends.py:175 tokens.py:67
msgid "Token is expired"
msgstr "توکن منقضی شده است"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "توکن نامعتبر است یا منقضی شده است"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "هیچ اکانت فعالی برای اطلاعات داده شده یافت نشد"

#: serializers.py:108
msgid "No active account found for the given token."
msgstr "هیچ اکانت فعالی برای توکن داده شده یافت نشد"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "توکن به لیست سیاه رفته است"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr "تنظیمات '{}' حذف شده است. لطفا به '{}' برای تنظیمات موجود مراجعه کنید."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "شناسه توکن (jti)"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "کاربر"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "زمان ایجاد"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "زمان انقضا"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "لیست سیاه توکن"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr "توکن برجسته"

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr "توکن‌های برجسته"

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr "توکن برای %(user)s (%(jti)s)"

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr "توکن در لیست سیاه"

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr "توکن‌های لیست سیاه"

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr "توکن لیست سیاه برای %(user)s"

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "توکن بدون هیچ نوع و طول عمر قابل ساخت نیست"

#: tokens.py:127
msgid "Token has no id"
msgstr "توکن id ندارد"

#: tokens.py:139
msgid "Token has no type"
msgstr "توکن نوع ندارد"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "توکن نوع اشتباهی دارد"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "توکن دارای '{}' claim نمی‌باشد"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "'{}' claim توکن منقضی شده"
