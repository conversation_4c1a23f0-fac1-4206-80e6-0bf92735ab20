import React, { useState, useEffect } from "react";
import api from "../api";

const Home = () => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);

   useEffect(() => {
    console.log("useEffect called");
    getNotes();
  }, []);
 
  const getNotes = async () => {
    try {
      const res = await api.get("api/notes/");
      console.log("Notes response:", res.data);
      setNotes(res.data);
    } catch (error) {
      console.error("Error fetching notes:", error);
    } finally {
      setLoading(false);
    }
  };

 
  if (loading) {
    return <div>Loading notes...</div>;
  }

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">My Notes</h2>
      {notes.length === 0 ? (
        <p>No notes available.</p>
      ) : (
        <ul className="space-y-3">
          {notes.map((note) => (
            <li
              key={note.id}
              className="p-4 border rounded-lg shadow-sm bg-white"
            >
              <h3 className="font-semibold">{note.title}</h3>
              <p className="text-gray-600">{note.content}</p>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Home;
