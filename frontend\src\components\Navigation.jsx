import React from "react";
import { Link } from "react-router-dom";

const Navigation = () => {
  return (
    <nav className="bg-gray-800 p-4 flex justify-between items-center">
      <h2 className="text-white text-xl font-bold">Notes App</h2>
      <ul className="flex space-x-6">
        <li>
          <Link to="/" className="text-white hover:text-yellow-400">
            Notes
          </Link>
        </li>
        <li>
          <Link to="/notes/add" className="text-white hover:text-yellow-400">
            Add
          </Link>
        </li>
        <li>
          <Link to="/notes/update/1" className="text-white hover:text-yellow-400">
            Update
          </Link>
        </li>
        {/* ✅ Logout button */}
        <li>
          <Link
            to="/logout"
            className="bg-red-600 px-3 py-1 rounded text-white font-semibold hover:bg-red-700 transition"
          >
            Logout
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default Navigation;
