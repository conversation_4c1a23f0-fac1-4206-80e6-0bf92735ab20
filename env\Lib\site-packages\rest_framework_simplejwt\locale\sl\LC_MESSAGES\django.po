# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-28 15:09-0300\n"
"PO-Revision-Date: \n"
"Last-Translator: Urban Prevc <<EMAIL>>\n"
"Language-Team: \n"
"Language: sl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.6\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Glava 'Authorization' mora vsebovati dve vrednos<PERSON>, lo<PERSON><PERSON> s presledkom"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Podan žeton ni veljaven za nobeno vrsto žetona"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "Žeton ni vseboval prepoznavne identifikacije uporabnika"

#: authentication.py:135
msgid "User not found"
msgstr "Uporabnik ni najden"

#: authentication.py:139
msgid "User is inactive"
msgstr "Uporabnik je neaktiven"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Neprepoznana vrsta algoritma'{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Za uporabo '{}' je potrebna namestitev 'cryptography'."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""
"Neprepoznana vrsta '{}', 'leeway' mora biti vrste int, float ali timedelta."

#: backends.py:125 backends.py:177 tokens.py:69
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Žeton je neveljaven ali potekel"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "Naveden algoritem je neveljaven"

#: backends.py:175 tokens.py:67
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Žeton je neveljaven ali potekel"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Žeton je neveljaven ali potekel"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Aktiven račun s podanimi poverilnicami ni najden"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Aktiven račun s podanimi poverilnicami ni najden"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "Žeton je na črnem seznamu"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Nastavitev '{}' je bila odstranjena. Prosimo, oglejte si '{}' za "
"razpoložljive nastavitve."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "uporabnik"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "ustvarjen ob"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "poteče ob"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Črni seznam žetonov"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr ""

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr ""

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr ""

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr ""

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr ""

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr ""

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "Ni mogoče ustvariti žetona brez vrste ali življenjske dobe"

#: tokens.py:127
msgid "Token has no id"
msgstr "Žetonu manjka id"

#: tokens.py:139
msgid "Token has no type"
msgstr "Žetonu manjka vrsta"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "Žeton je napačne vrste"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "Žeton nima '{}' zahtevka"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "'{}' zahtevek žetona je potekel"
