import React, { useState, useEffect } from "react"
import { useParams, useNavigate } from "react-router-dom"
import api from "../api"
import { ACCESS_TOKEN } from "../constant"

const UpdateNotePage = () => {
  const { id } = useParams() // URL se note id milegi
  const navigate = useNavigate()

  const [note, setNote] = useState({ title: "", content: "" })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  // ✅ Note load karo jab page open ho
  useEffect(() => {
    const fetchNote = async () => {
      try {
        const response = await api.get(`/api/notes/${id}/`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem(ACCESS_TOKEN)}`
          }
        })
        setNote(response.data)
        setLoading(false)
      } catch (err) {
        setError("Failed to fetch note")
        setLoading(false)
      }
    }
    fetchNote()
  }, [id])

  // ✅ Input change handle karo
  const handleChange = (e) => {
    setNote({ ...note, [e.target.name]: e.target.value })
  }

  // ✅ Note update request bhejo
  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      await api.put(`/api/notes/${id}/`, note, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem(ACCESS_TOKEN)}`
        }
      })
      navigate("/") // update hone ke baad home page
    } catch (err) {
      setError("Failed to update note")
    }
  }

  if (loading) return <p className="text-center mt-10">Loading...</p>

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <form
        onSubmit={handleSubmit}
        className="bg-white shadow-lg rounded-2xl p-6 w-full max-w-lg"
      >
        <h1 className="text-2xl font-bold mb-6 text-center">Update Note</h1>

        {error && <p className="text-red-500 mb-4">{error}</p>}

        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Title</label>
          <input
            type="text"
            name="title"
            value={note.title}
            onChange={handleChange}
            required
            className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
          />
        </div>

        <div className="mb-6">
          <label className="block text-gray-700 mb-2">Content</label>
          <textarea
            name="content"
            value={note.content}
            onChange={handleChange}
            required
            rows="6"
            className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
          ></textarea>
        </div>

        <button
          type="submit"
          className="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition"
        >
          Update Note
        </button>
      </form>
    </div>
  )
}

export default UpdateNotePage
