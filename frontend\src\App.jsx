import React from 'react'
import {Routes, Route, Navigate} from 'react-router-dom'
import Home from './pages/Home'
import Login from './pages/Login'
import Register from './pages/Register'
import Notfound from './pages/Notfound'
import ProtectedRoute from './components/ProtectedRoute'
import AddNotePage from './pages/AddNotePage'
import UpdateNotePage from './pages/UpdateNotePage'
import Navigation from './components/Navigation'

const Logout = () => {
  localStorage.clear()
  return <Navigate to="/login" />
}

const RegisterAndLogout = () => {
  localStorage.clear()
  return <Register />
}

const App = () => {
  return (
    <>
    <Navigation />
      <Routes>
        <Route path='/' element={<ProtectedRoute />}>
          <Route index element={<Home />} />
           <Route path="/notes/add" element={<AddNotePage />} />
          <Route path="/notes/update/:id" element={<UpdateNotePage />} />
        </Route>
        <Route path='/login' element={<Login />} />
        <Route path='/register' element={<RegisterAndLogout />} />
        <Route path='/logout' element={<Logout />} />
        <Route path='*' element={<Notfound />} />
      </Routes>
    </>
  )
}

export default App
