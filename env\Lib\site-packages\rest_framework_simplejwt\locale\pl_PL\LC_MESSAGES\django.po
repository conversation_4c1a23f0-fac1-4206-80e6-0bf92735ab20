# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-28 15:09-0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: pl_PL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "Nagłówek autoryzacji musi zawierać dwie wartości rodzielone spacjami"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Podany token jest błędny dla każdego typu tokena"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "Token nie zawierał rozpoznawalnej identyfikacji użytkownika"

#: authentication.py:135
msgid "User not found"
msgstr "Użytkownik nie znaleziony"

#: authentication.py:139
msgid "User is inactive"
msgstr "Użytkownik jest nieaktywny"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Nierozpoznany typ algorytmu '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:69
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token jest niepoprawny lub wygasł"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr ""

#: backends.py:175 tokens.py:67
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token jest niepoprawny lub wygasł"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token jest niepoprawny lub wygasł"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Nie znaleziono aktywnego konta dla podanych danych uwierzytelniających"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Nie znaleziono aktywnego konta dla podanych danych uwierzytelniających"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "Token znajduję się na czarnej liście"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Ustawienie '{}' zostało usunięte. Dostępne ustawienia znajdują sie w '{}'"

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "użytkownik"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "stworzony w"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "wygasa o"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr ""

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr ""

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr ""

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr ""

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr ""

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr ""

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "Nie można utworzyć tokena bez podanego typu lub żywotności"

#: tokens.py:127
msgid "Token has no id"
msgstr "Token nie posiada numeru identyfikacyjnego"

#: tokens.py:139
msgid "Token has no type"
msgstr "Token nie posiada typu"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "Token posiada zły typ"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "Token nie posiada upoważnienia '{}'"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "Upoważnienie tokena '{}' wygasło"
